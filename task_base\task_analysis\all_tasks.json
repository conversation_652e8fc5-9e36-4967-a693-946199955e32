[{"task_id": "1", "description": "Assist in rapidly monitoring the nearby forest areas.", "home_position": [42.29617690980287, -82.6623000857478, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[42.29718194618623, -82.66589734800576], [42.29727802176437, -82.65925867248248], [42.293533588741575, -82.65931927207117], [42.29357539942106, -82.66618960254094]]}, "task_types": ["Survey task"], "subtasks": [{"subtask_id": "1", "type": "Survey task", "target_area_id": "area_3", "target_area_name": "forest", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[42.29602833822704, -82.6611113548279], [42.29603839499092, -82.65998294950887], [42.29463146264805, -82.65998912414624], [42.294610799330975, -82.66115630230765]]]}, "parameters": {"wtask": -0.3, "wsemantic": -0.7, "frontal_overlap": 68.3, "lateral_overlap": 58.3, "gsd": 3.99}, "error": null}]}, {"task_id": "2", "description": "Conduct quick farmland monitoring and detailed warehouse scanning.", "home_position": [42.29617690980287, -82.6623000857478, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[42.29718194618623, -82.66589734800576], [42.29727802176437, -82.65925867248248], [42.293533588741575, -82.65931927207117], [42.29357539942106, -82.66618960254094]]}, "task_types": ["Survey task", "Structure scan task"], "subtasks": [{"subtask_id": "1", "type": "Survey task", "target_area_id": "area_4", "target_area_name": "farmland_1", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[42.296105987033755, -82.66194864827698], [42.296086178491265, -82.66111846788824], [42.29414887143721, -82.66119880793259], [42.29417660425833, -82.66358222901644], [42.29531363987361, -82.66353938099742], [42.29530967810206, -82.66200756430021]]]}, "parameters": {"wtask": -0.3, "wsemantic": -0.7, "frontal_overlap": 68.3, "lateral_overlap": 58.3, "gsd": 3.99}, "error": null}, {"subtask_id": "2", "type": "Survey task", "target_area_id": "area_5", "target_area_name": "farmland_2", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[42.295499844004155, -82.66530150559512], [42.295425206683475, -82.66373991966249], [42.29417660590377, -82.66387680893385], [42.29426772798603, -82.66544076166272]]]}, "parameters": {"wtask": -0.3, "wsemantic": -0.7, "frontal_overlap": 68.3, "lateral_overlap": 58.3, "gsd": 3.99}, "error": null}, {"subtask_id": "3", "type": "Structure scan task", "target_area_id": "area_2", "target_area_name": "warehouse", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[42.2956037604317, -82.66209304332735], [42.295533628866124, -82.66209429118956], [42.29553216869182, -82.6623469692514], [42.29561539836005, -82.66234104712748]]]}, "parameters": {"wtask": 0.8, "wsemantic": 0.9, "height": 8, "frontal_overlap": 87.6, "lateral_overlap": 77.6, "gsd": 1.16}, "error": null}]}, {"task_id": "3", "description": "Conduct road inspection.", "home_position": [42.29617690980287, -82.6623000857478, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[42.29718194618623, -82.66589734800576], [42.29727802176437, -82.65925867248248], [42.293533588741575, -82.65931927207117], [42.29357539942106, -82.66618960254094]]}, "task_types": ["Corridor scan task"], "subtasks": [{"subtask_id": "1", "type": "Corridor scan task", "target_area_id": "area_6", "target_area_name": "road_1", "feasible": true, "geometry": {"type": "LineString", "coordinates": [[42.29536911002327, -82.66359829684507], [42.29693002305041, -82.66351260078383]]}, "parameters": {"wtask": 0.3, "wsemantic": 0.2, "width": 10, "frontal_overlap": 78.9, "lateral_overlap": 68.9, "gsd": 2.42}, "error": null}, {"subtask_id": "2", "type": "Corridor scan task", "target_area_id": "area_7", "target_area_name": "road_2", "feasible": true, "geometry": {"type": "LineString", "coordinates": [[42.29691417640369, -82.66347510875849], [42.297052834367285, -82.6612416557528]]}, "parameters": {"wtask": 0.3, "wsemantic": 0.2, "width": 10, "frontal_overlap": 78.9, "lateral_overlap": 68.9, "gsd": 2.42}, "error": null}]}, {"task_id": "4", "description": "Execute agricultural zone scanning with 70% frontal overlap, 40% lateral overlap, and a ground sampling distance (GSD) of 0.9 cm/pixel.", "home_position": [42.29617690980287, -82.6623000857478, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[42.29718194618623, -82.66589734800576], [42.29727802176437, -82.65925867248248], [42.293533588741575, -82.65931927207117], [42.29357539942106, -82.66618960254094]]}, "task_types": ["Survey task"], "subtasks": [{"subtask_id": "1", "type": "Survey task", "target_area_id": "area_4", "target_area_name": "farmland_1", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[42.296105987033755, -82.66194864827698], [42.296086178491265, -82.66111846788824], [42.29414887143721, -82.66119880793259], [42.29417660425833, -82.66358222901644], [42.29531363987361, -82.66353938099742], [42.29530967810206, -82.66200756430021]]]}, "parameters": {"frontal_overlap": 76.9, "lateral_overlap": 66.9, "gsd": 2.72, "wtask": 0.2, "wsemantic": 0.0}, "error": null}, {"subtask_id": "2", "type": "Survey task", "target_area_id": "area_5", "target_area_name": "farmland_2", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[42.295499844004155, -82.66530150559512], [42.295425206683475, -82.66373991966249], [42.29417660590377, -82.66387680893385], [42.29426772798603, -82.66544076166272]]]}, "parameters": {"frontal_overlap": 76.9, "lateral_overlap": 66.9, "gsd": 2.72, "wtask": 0.2, "wsemantic": 0.0}, "error": null}]}, {"task_id": "5", "description": "Execute sequential navigation at a constant altitude of 40 meters through the following waypoints in designated order:[[42.29466064621194,-82.65989487315737],[42.29465191789008,-82.66121646253347],[42.2969256036579,-82.66115746300308],[42.29692123964465,-82.65986537341772]]", "home_position": [42.29617690980287, -82.6623000857478, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[42.29718194618623, -82.66589734800576], [42.29727802176437, -82.65925867248248], [42.293533588741575, -82.65931927207117], [42.29357539942106, -82.66618960254094]]}, "task_types": ["Simple waypoint task"], "subtasks": [{"subtask_id": "1", "type": "Simple waypoint task", "target_area_id": null, "target_area_name": null, "feasible": true, "geometry": {"type": "MultiPoint", "coordinates": [[42.29466064621194, -82.65989487315737], [42.29465191789008, -82.66121646253347], [42.2969256036579, -82.66115746300308], [42.29692123964465, -82.65986537341772]]}, "parameters": {"height": 40}, "error": null}]}, {"task_id": "7", "description": "Check the roof of the house for any issues.", "home_position": [42.29617690980287, -82.6623000857478, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[42.29718194618623, -82.66589734800576], [42.29727802176437, -82.65925867248248], [42.293533588741575, -82.65931927207117], [42.29357539942106, -82.66618960254094]]}, "task_types": ["Structure scan task"], "subtasks": [{"subtask_id": "1", "type": "Structure scan task", "target_area_id": "area_1", "target_area_name": "house", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[42.296441539941, -82.66197309544255], [42.2960583754255, -82.66195611125993], [42.29607617273872, -82.66271049164818], [42.29644886820302, -82.66270199955076]]]}, "parameters": {"wtask": 0.7, "wsemantic": 0.3, "height": null, "frontal_overlap": 83.3, "lateral_overlap": 73.3, "gsd": 1.79}, "error": null}]}, {"task_id": "8", "description": "Fly along the edge of the forest and make sure everything looks normal.", "home_position": [42.29617690980287, -82.6623000857478, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[42.29718194618623, -82.66589734800576], [42.29727802176437, -82.65925867248248], [42.293533588741575, -82.65931927207117], [42.29357539942106, -82.66618960254094]]}, "task_types": ["Corridor scan task"], "subtasks": [{"subtask_id": "1", "type": "Corridor scan task", "target_area_id": "area_3", "target_area_name": "forest", "feasible": true, "geometry": {"type": "LineString", "coordinates": [[42.29602833822704, -82.6611113548279], [42.29603839499092, -82.65998294950887], [42.29463146264805, -82.65998912414624], [42.294610799330975, -82.66115630230765], [42.29602833822704, -82.6611113548279]]}, "parameters": {"wtask": 0.3, "wsemantic": -0.4, "width": 20, "frontal_overlap": 75.6, "lateral_overlap": 65.6, "gsd": 2.91}, "error": null}]}, {"task_id": "9", "description": "Capture high-resolution imagery of the farmland to check crop health.", "home_position": [42.29617690980287, -82.6623000857478, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[42.29718194618623, -82.66589734800576], [42.29727802176437, -82.65925867248248], [42.293533588741575, -82.65931927207117], [42.29357539942106, -82.66618960254094]]}, "task_types": ["Survey task"], "subtasks": [{"subtask_id": "1", "type": "Survey task", "target_area_id": "area_4", "target_area_name": "farmland_1", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[42.296105987033755, -82.66194864827698], [42.296086178491265, -82.66111846788824], [42.29414887143721, -82.66119880793259], [42.29417660425833, -82.66358222901644], [42.29531363987361, -82.66353938099742], [42.29530967810206, -82.66200756430021]]]}, "parameters": {"wtask": 0.3, "wsemantic": 0.7, "gsd": 2.01, "frontal_overlap": 81.7, "lateral_overlap": 71.7}, "error": null}, {"subtask_id": "2", "type": "Survey task", "target_area_id": "area_5", "target_area_name": "farmland_2", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[42.295499844004155, -82.66530150559512], [42.295425206683475, -82.66373991966249], [42.29417660590377, -82.66387680893385], [42.29426772798603, -82.66544076166272]]]}, "parameters": {"wtask": 0.3, "wsemantic": 0.7, "gsd": 2.01, "frontal_overlap": 81.7, "lateral_overlap": 71.7}, "error": null}]}, {"task_id": "10", "description": "Inspect the exterior walls of the warehouse, especially the upper parts.", "home_position": [42.29617690980287, -82.6623000857478, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[42.29718194618623, -82.66589734800576], [42.29727802176437, -82.65925867248248], [42.293533588741575, -82.65931927207117], [42.29357539942106, -82.66618960254094]]}, "task_types": ["Structure scan task"], "subtasks": [{"subtask_id": "1", "type": "Structure scan task", "target_area_id": "area_2", "target_area_name": "warehouse", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[42.2956037604317, -82.66209304332735], [42.295533628866124, -82.66209429118956], [42.29553216869182, -82.6623469692514], [42.29561539836005, -82.66234104712748]]]}, "parameters": {"wtask": 0.7, "wsemantic": 0.6, "height": 8, "frontal_overlap": 84.9, "lateral_overlap": 74.9, "gsd": 1.54}, "error": null}]}, {"task_id": "11", "description": "Get a visual survey of the yard area.", "home_position": [42.29617690980287, -82.6623000857478, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[42.29718194618623, -82.66589734800576], [42.29727802176437, -82.65925867248248], [42.293533588741575, -82.65931927207117], [42.29357539942106, -82.66618960254094]]}, "task_types": ["Survey task"], "subtasks": [{"subtask_id": "1", "type": "Survey task", "target_area_id": "area_8", "target_area_name": "yard", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[42.296244652305, -82.66274669243523], [42.296240690592015, -82.66194864805497], [42.29532949243418, -82.66198614008032], [42.29537361790283, -82.6628226041794]]]}, "parameters": {"wtask": 0.2, "wsemantic": 0.3, "frontal_overlap": 78.6, "lateral_overlap": 68.6, "gsd": 2.48}, "error": null}]}, {"task_id": "12", "description": "Fly a path along the road (road_1) and take some photos.", "home_position": [42.29617690980287, -82.6623000857478, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[42.29718194618623, -82.66589734800576], [42.29727802176437, -82.65925867248248], [42.293533588741575, -82.65931927207117], [42.29357539942106, -82.66618960254094]]}, "task_types": ["Corridor scan task"], "subtasks": [{"subtask_id": "1", "type": "Corridor scan task", "target_area_id": "area_6", "target_area_name": "road_1", "feasible": true, "geometry": {"type": "LineString", "coordinates": [[42.29536911002327, -82.66359829684507], [42.29693002305041, -82.66351260078383]]}, "parameters": {"wtask": 0.2, "wsemantic": -0.3, "width": 10, "frontal_overlap": 75.2, "lateral_overlap": 65.2, "gsd": 2.97}, "error": null}]}, {"task_id": "13", "description": "Perform a quick scan over the entire property including the house and yard.", "home_position": [42.29617690980287, -82.6623000857478, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[42.29718194618623, -82.66589734800576], [42.29727802176437, -82.65925867248248], [42.293533588741575, -82.65931927207117], [42.29357539942106, -82.66618960254094]]}, "task_types": ["Survey task"], "subtasks": [{"subtask_id": "1", "type": "Survey task", "target_area_id": "area_1", "target_area_name": "house", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[42.296441539941, -82.66197309544255], [42.2960583754255, -82.66195611125993], [42.29607617273872, -82.66271049164818], [42.29644886820302, -82.66270199955076]]]}, "parameters": {"wtask": 0.3, "wsemantic": -0.7, "frontal_overlap": 74.0, "lateral_overlap": 64.0, "gsd": 3.15}, "error": null}, {"subtask_id": "2", "type": "Survey task", "target_area_id": "area_8", "target_area_name": "yard", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[42.296244652305, -82.66274669243523], [42.296240690592015, -82.66194864805497], [42.29532949243418, -82.66198614008032], [42.29537361790283, -82.6628226041794]]]}, "parameters": {"wtask": -0.5, "wsemantic": -0.7, "frontal_overlap": 66.4, "lateral_overlap": 56.4, "gsd": 4.26}, "error": null}]}, {"task_id": "14", "description": "Perform a detailed inspection of the bridge south of the farmland.", "home_position": [42.29617690980287, -82.6623000857478, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[42.29718194618623, -82.66589734800576], [42.29727802176437, -82.65925867248248], [42.293533588741575, -82.65931927207117], [42.29357539942106, -82.66618960254094]]}, "task_types": ["Structure scan task"], "subtasks": [{"subtask_id": "1", "type": "Structure scan task", "target_area_id": "bridge_south_of_farmland", "target_area_name": "Bridge south of farmland", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[42.2953, -82.6636], [42.2953, -82.6637], [42.2954, -82.6637], [42.2954, -82.6636], [42.2953, -82.6636]]}, "parameters": {"wtask": 0.8, "wsemantic": 0.9, "height": 15, "frontal_overlap": 87.6, "lateral_overlap": 77.6, "gsd": 1.16}, "error": null}]}, {"task_id": "15", "description": "Perform a polygon scan of road_2 with 80% overlap.", "home_position": [42.29617690980287, -82.6623000857478, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[42.29718194618623, -82.66589734800576], [42.29727802176437, -82.65925867248248], [42.293533588741575, -82.65931927207117], [42.29357539942106, -82.66618960254094]]}, "task_types": ["Corridor scan task"], "subtasks": [{"subtask_id": "1", "type": "Corridor scan task", "target_area_id": "area_7", "target_area_name": "road_2", "feasible": true, "geometry": {"type": "LineString", "coordinates": [[42.29691417640369, -82.66347510875849], [42.297052834367285, -82.6612416557528]]}, "parameters": {"frontal_overlap": 79.7, "lateral_overlap": 69.7, "width": 10, "wtask": 0.2, "wsemantic": 0.5, "gsd": 2.32}, "error": null}]}]