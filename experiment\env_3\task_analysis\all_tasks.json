[{"task_id": "1", "description": "Inspect the library roof for any water damage or structural issues.", "error": "Error code: 401 - {'error': {'message': 'User not found.', 'code': 401}}", "task_types": ["error"], "task_summary": "任务处理失败。系统在分析您的请求时遇到了技术问题，无法完成任务规划。请稍后重试或修改您的任务描述。", "feasibility_analysis": "处理任务时发生错误，无法确定任务可行性", "subtasks": []}, {"task_id": "2", "description": "Check the condition of the football field and track to identify any areas needing maintenance.", "error": "Error code: 401 - {'error': {'message': 'User not found.', 'code': 401}}", "task_types": ["error"], "task_summary": "任务处理失败。系统在分析您的请求时遇到了技术问题，无法完成任务规划。请稍后重试或修改您的任务描述。", "feasibility_analysis": "处理任务时发生错误，无法确定任务可行性", "subtasks": []}, {"task_id": "3", "description": "Survey the dormitory area to assess external building conditions and nearby facilities.", "error": "Error code: 401 - {'error': {'message': 'User not found.', 'code': 401}}", "task_types": ["error"], "task_summary": "任务处理失败。系统在分析您的请求时遇到了技术问题，无法完成任务规划。请稍后重试或修改您的任务描述。", "feasibility_analysis": "处理任务时发生错误，无法确定任务可行性", "subtasks": []}, {"task_id": "4", "description": "Capture high-resolution photos of Yingxue Lake to monitor water levels and surrounding vegetation.", "error": "Error code: 401 - {'error': {'message': 'User not found.', 'code': 401}}", "task_types": ["error"], "task_summary": "任务处理失败。系统在分析您的请求时遇到了技术问题，无法完成任务规划。请稍后重试或修改您的任务描述。", "feasibility_analysis": "处理任务时发生错误，无法确定任务可行性", "subtasks": []}, {"task_id": "5", "description": "Create a detailed map of the basketball courts with a ground sampling distance (GSD) of 1.6 cm/pixel.", "error": "Error code: 401 - {'error': {'message': 'User not found.', 'code': 401}}", "task_types": ["error"], "task_summary": "任务处理失败。系统在分析您的请求时遇到了技术问题，无法完成任务规划。请稍后重试或修改您的任务描述。", "feasibility_analysis": "处理任务时发生错误，无法确定任务可行性", "subtasks": []}, {"task_id": "6", "description": "Execute sequential navigation at 50m altitude through the following waypoints: [[36.682395, 117.177048], [36.684210, 117.177322], [36.684180, 117.179540], [36.681520, 117.179120]]", "error": "Error code: 401 - {'error': {'message': 'User not found.', 'code': 401}}", "task_types": ["error"], "task_summary": "任务处理失败。系统在分析您的请求时遇到了技术问题，无法完成任务规划。请稍后重试或修改您的任务描述。", "feasibility_analysis": "处理任务时发生错误，无法确定任务可行性", "subtasks": []}, {"task_id": "7", "description": "Perform a comprehensive scan of the teaching building exterior with 60% frontal overlap and 50% side overlap.", "error": "Error code: 401 - {'error': {'message': 'User not found.', 'code': 401}}", "task_types": ["error"], "task_summary": "任务处理失败。系统在分析您的请求时遇到了技术问题，无法完成任务规划。请稍后重试或修改您的任务描述。", "feasibility_analysis": "处理任务时发生错误，无法确定任务可行性", "subtasks": []}, {"task_id": "8", "description": "Fly along the main road and capture photos to document campus traffic flow.", "error": "Error code: 401 - {'error': {'message': 'User not found.', 'code': 401}}", "task_types": ["error"], "task_summary": "任务处理失败。系统在分析您的请求时遇到了技术问题，无法完成任务规划。请稍后重试或修改您的任务描述。", "feasibility_analysis": "处理任务时发生错误，无法确定任务可行性", "subtasks": []}, {"task_id": "9", "description": "Photograph the laboratory building from multiple angles to document its current state.", "error": "Error code: 401 - {'error': {'message': 'User not found.', 'code': 401}}", "task_types": ["error"], "task_summary": "任务处理失败。系统在分析您的请求时遇到了技术问题，无法完成任务规划。请稍后重试或修改您的任务描述。", "feasibility_analysis": "处理任务时发生错误，无法确定任务可行性", "subtasks": []}, {"task_id": "10", "description": "Inspect the cafeteria roof and surrounding area for potential maintenance issues.", "error": "Error code: 401 - {'error': {'message': 'User not found.', 'code': 401}}", "task_types": ["error"], "task_summary": "任务处理失败。系统在分析您的请求时遇到了技术问题，无法完成任务规划。请稍后重试或修改您的任务描述。", "feasibility_analysis": "处理任务时发生错误，无法确定任务可行性", "subtasks": []}, {"task_id": "11", "description": "Create an orthomosaic map of the entire campus with a GSD of 4 cm/pixel.", "error": "Error code: 401 - {'error': {'message': 'User not found.', 'code': 401}}", "task_types": ["error"], "task_summary": "任务处理失败。系统在分析您的请求时遇到了技术问题，无法完成任务规划。请稍后重试或修改您的任务描述。", "feasibility_analysis": "处理任务时发生错误，无法确定任务可行性", "subtasks": []}, {"task_id": "12", "description": "Capture aerial photos of the campus entrance and main roads for campus navigation materials.", "error": "Error code: 401 - {'error': {'message': 'User not found.', 'code': 401}}", "task_types": ["error"], "task_summary": "任务处理失败。系统在分析您的请求时遇到了技术问题，无法完成任务规划。请稍后重试或修改您的任务描述。", "feasibility_analysis": "处理任务时发生错误，无法确定任务可行性", "subtasks": []}]