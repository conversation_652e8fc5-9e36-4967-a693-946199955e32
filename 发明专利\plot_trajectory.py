#!/usr/bin/env python3
"""
PX4飞行日志可视化程序
支持.ulg格式日志文件的3D轨迹显示
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import pandas as pd
from pyulog import ULog
import os
import sys

class PX4LogVisualizer:
    def __init__(self, log_file_path):
        """
        初始化PX4日志可视化器
        
        Args:
            log_file_path (str): .ulg日志文件路径
        """
        self.log_file_path = log_file_path
        self.ulog = None
        self.position_data = None
        self.attitude_data = None
        self.velocity_data = None
        
    def load_log(self):
        """加载ULog文件"""
        try:
            print(f"正在加载日志文件: {self.log_file_path}")
            self.ulog = ULog(self.log_file_path)
            print("日志文件加载成功!")
            return True
        except Exception as e:
            print(f"加载日志文件失败: {e}")
            return False
    
    def extract_position_data(self):
        """提取位置数据"""
        try:
            # 尝试获取vehicle_local_position数据
            position_msgs = []
            for msg in self.ulog.data_list:
                if msg.name == 'vehicle_local_position':
                    position_msgs.append(msg)
            
            if not position_msgs:
                print("未找到位置数据 (vehicle_local_position)")
                return False
            
            # 使用第一个找到的位置消息
            pos_msg = position_msgs[0]
            
            # 提取时间戳和位置数据
            timestamps = pos_msg.data['timestamp'] / 1e6  # 转换为秒
            x = pos_msg.data['x']
            y = pos_msg.data['y']
            z = pos_msg.data['z']
            
            self.position_data = pd.DataFrame({
                'timestamp': timestamps,
                'x': x,
                'y': y,
                'z': z
            })
            
            print(f"成功提取位置数据，共{len(self.position_data)}个数据点")
            return True
            
        except Exception as e:
            print(f"提取位置数据失败: {e}")
            return False
    
    def extract_attitude_data(self):
        """提取姿态数据"""
        try:
            attitude_msgs = []
            for msg in self.ulog.data_list:
                if msg.name == 'vehicle_attitude':
                    attitude_msgs.append(msg)
            
            if not attitude_msgs:
                print("未找到姿态数据 (vehicle_attitude)")
                return False
            
            att_msg = attitude_msgs[0]
            timestamps = att_msg.data['timestamp'] / 1e6
            
            # 提取四元数
            q0 = att_msg.data['q[0]']
            q1 = att_msg.data['q[1]']
            q2 = att_msg.data['q[2]']
            q3 = att_msg.data['q[3]']
            
            # 转换为欧拉角 (roll, pitch, yaw)
            roll = np.arctan2(2 * (q0 * q1 + q2 * q3), 1 - 2 * (q1**2 + q2**2))
            pitch = np.arcsin(2 * (q0 * q2 - q3 * q1))
            yaw = np.arctan2(2 * (q0 * q3 + q1 * q2), 1 - 2 * (q2**2 + q3**2))
            
            self.attitude_data = pd.DataFrame({
                'timestamp': timestamps,
                'roll': np.degrees(roll),
                'pitch': np.degrees(pitch),
                'yaw': np.degrees(yaw)
            })
            
            print(f"成功提取姿态数据，共{len(self.attitude_data)}个数据点")
            return True
            
        except Exception as e:
            print(f"提取姿态数据失败: {e}")
            return False
    
    def extract_velocity_data(self):
        """提取速度数据"""
        try:
            if self.position_data is None:
                return False
                
            # 计算速度 (位置的导数)
            dt = np.diff(self.position_data['timestamp'])
            vx = np.diff(self.position_data['x']) / dt
            vy = np.diff(self.position_data['y']) / dt
            vz = np.diff(self.position_data['z']) / dt
            
            # 添加第一个点的速度为0
            vx = np.insert(vx, 0, 0)
            vy = np.insert(vy, 0, 0)
            vz = np.insert(vz, 0, 0)
            
            self.velocity_data = pd.DataFrame({
                'timestamp': self.position_data['timestamp'],
                'vx': vx,
                'vy': vy,
                'vz': vz,
                'speed': np.sqrt(vx**2 + vy**2 + vz**2)
            })
            
            print(f"成功计算速度数据")
            return True
            
        except Exception as e:
            print(f"计算速度数据失败: {e}")
            return False
    
    def plot_3d_trajectory(self):
        """绘制3D飞行轨迹"""
        if self.position_data is None:
            print("没有位置数据可供显示")
            return
        
        fig = plt.figure(figsize=(12, 8))
        ax = fig.add_subplot(111, projection='3d')
        
        # 绘制轨迹线
        x, y, z = self.position_data['x'], self.position_data['y'], -self.position_data['z']  # Z轴翻转以符合常规显示
        
        # 根据时间着色
        time_normalized = (self.position_data['timestamp'] - self.position_data['timestamp'].min()) / \
                         (self.position_data['timestamp'].max() - self.position_data['timestamp'].min())
        
        scatter = ax.scatter(x, y, z, c=time_normalized, cmap='viridis', s=1, alpha=0.7)
        
        # 标记起点和终点
        ax.scatter(x.iloc[0], y.iloc[0], z.iloc[0], color='green', s=100, label='起点')
        ax.scatter(x.iloc[-1], y.iloc[-1], z.iloc[-1], color='red', s=100, label='终点')
        
        # 添加颜色条
        plt.colorbar(scatter, ax=ax, label='飞行时间 (归一化)')
        
        # 设置标签和标题
        ax.set_xlabel('X (米)')
        ax.set_ylabel('Y (米)')
        ax.set_zlabel('高度 (米)')
        ax.set_title('PX4 飞行轨迹 - 3D视图')
        ax.legend()
        
        # 设置相等的坐标轴比例
        ax.set_box_aspect([1,1,0.5])
        
        plt.tight_layout()
        plt.show()
    
    def plot_2d_trajectory(self):
        """绘制2D俯视图轨迹"""
        if self.position_data is None:
            print("没有位置数据可供显示")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 俯视图 (X-Y)
        ax1 = axes[0, 0]
        x, y = self.position_data['x'], self.position_data['y']
        ax1.plot(x, y, 'b-', linewidth=1, alpha=0.7)
        ax1.scatter(x.iloc[0], y.iloc[0], color='green', s=100, label='起点', zorder=5)
        ax1.scatter(x.iloc[-1], y.iloc[-1], color='red', s=100, label='终点', zorder=5)
        ax1.set_xlabel('X (米)')
        ax1.set_ylabel('Y (米)')
        ax1.set_title('俯视图 (X-Y平面)')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        ax1.axis('equal')
        
        # 高度时间序列
        ax2 = axes[0, 1]
        time_rel = self.position_data['timestamp'] - self.position_data['timestamp'].min()
        ax2.plot(time_rel, -self.position_data['z'], 'r-', linewidth=1)
        ax2.set_xlabel('时间 (秒)')
        ax2.set_ylabel('高度 (米)')
        ax2.set_title('高度 vs 时间')
        ax2.grid(True, alpha=0.3)
        
        # 侧视图 (X-Z)
        ax3 = axes[1, 0]
        ax3.plot(x, -self.position_data['z'], 'g-', linewidth=1, alpha=0.7)
        ax3.scatter(x.iloc[0], -self.position_data['z'].iloc[0], color='green', s=100, label='起点')
        ax3.scatter(x.iloc[-1], -self.position_data['z'].iloc[-1], color='red', s=100, label='终点')
        ax3.set_xlabel('X (米)')
        ax3.set_ylabel('高度 (米)')
        ax3.set_title('侧视图 (X-Z平面)')
        ax3.grid(True, alpha=0.3)
        ax3.legend()
        
        # 速度时间序列
        ax4 = axes[1, 1]
        if self.velocity_data is not None:
            time_rel_vel = self.velocity_data['timestamp'] - self.velocity_data['timestamp'].min()
            ax4.plot(time_rel_vel, self.velocity_data['speed'], 'purple', linewidth=1)
            ax4.set_xlabel('时间 (秒)')
            ax4.set_ylabel('速度 (m/s)')
            ax4.set_title('速度 vs 时间')
        else:
            ax4.text(0.5, 0.5, '速度数据不可用', ha='center', va='center', transform=ax4.transAxes)
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
    
    def plot_attitude(self):
        """绘制姿态角度图"""
        if self.attitude_data is None:
            print("没有姿态数据可供显示")
            return
        
        fig, axes = plt.subplots(3, 1, figsize=(12, 10))
        time_rel = self.attitude_data['timestamp'] - self.attitude_data['timestamp'].min()
        
        # Roll角
        axes[0].plot(time_rel, self.attitude_data['roll'], 'r-', linewidth=1)
        axes[0].set_ylabel('Roll (度)')
        axes[0].set_title('飞行器姿态角')
        axes[0].grid(True, alpha=0.3)
        
        # Pitch角
        axes[1].plot(time_rel, self.attitude_data['pitch'], 'g-', linewidth=1)
        axes[1].set_ylabel('Pitch (度)')
        axes[1].grid(True, alpha=0.3)
        
        # Yaw角
        axes[2].plot(time_rel, self.attitude_data['yaw'], 'b-', linewidth=1)
        axes[2].set_xlabel('时间 (秒)')
        axes[2].set_ylabel('Yaw (度)')
        axes[2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
    
    def print_flight_summary(self):
        """打印飞行摘要信息"""
        if self.position_data is None:
            print("无法生成飞行摘要：没有位置数据")
            return
        
        print("\n" + "="*50)
        print("飞行摘要")
        print("="*50)
        
        # 飞行时间
        total_time = self.position_data['timestamp'].max() - self.position_data['timestamp'].min()
        print(f"飞行时间: {total_time:.1f} 秒")
        
        # 飞行距离
        distances = np.sqrt(np.diff(self.position_data['x'])**2 + 
                           np.diff(self.position_data['y'])**2 + 
                           np.diff(self.position_data['z'])**2)
        total_distance = np.sum(distances)
        print(f"总飞行距离: {total_distance:.1f} 米")
        
        # 高度信息
        max_altitude = -self.position_data['z'].min()  # Z轴向下为正
        min_altitude = -self.position_data['z'].max()
        print(f"最大飞行高度: {max_altitude:.1f} 米")
        print(f"最小飞行高度: {min_altitude:.1f} 米")
        
        # 速度信息
        if self.velocity_data is not None:
            max_speed = self.velocity_data['speed'].max()
            avg_speed = self.velocity_data['speed'].mean()
            print(f"最大速度: {max_speed:.1f} m/s")
            print(f"平均速度: {avg_speed:.1f} m/s")
        
        print("="*50)
    
    def visualize(self):
        """完整的可视化流程"""
        # 检查文件是否存在
        if not os.path.exists(self.log_file_path):
            print(f"错误: 日志文件 '{self.log_file_path}' 不存在!")
            print("请确认文件路径正确，并且文件在当前目录中。")
            return False
        
        # 加载日志文件
        if not self.load_log():
            return False
        
        # 提取数据
        success = True
        success &= self.extract_position_data()
        success &= self.extract_attitude_data()
        success &= self.extract_velocity_data()
        
        if not success:
            print("数据提取失败，无法进行可视化")
            return False
        
        # 打印摘要
        self.print_flight_summary()
        
        # 生成图表
        print("\n正在生成可视化图表...")
        self.plot_3d_trajectory()
        self.plot_2d_trajectory()
        self.plot_attitude()
        
        print("可视化完成!")
        return True

def main():
    """主函数"""
    print("PX4飞行日志可视化程序")
    print("="*30)
    
    # 指定日志文件路径
    log_file = "02_56_08.ulg"
    
    # 创建可视化器实例
    visualizer = PX4LogVisualizer(log_file)
    
    # 执行可视化
    if visualizer.visualize():
        print("\n程序执行完成!")
    else:
        print("\n程序执行失败!")
        print("请检查:")
        print("1. 是否安装了所需依赖: pip install pyulog matplotlib pandas numpy")
        print("2. 日志文件是否存在且格式正确")

if __name__ == "__main__":
    main()