{"mission_prior": {"home_position": [42.29617690980287, -82.6623000857478, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[42.29718194618623, -82.66589734800576], [42.29727802176437, -82.65925867248248], [42.293533588741575, -82.65931927207117], [42.29357539942106, -82.66618960254094]]}, "target_areas": [{"id": "area_1", "name": "house", "type": "building", "geometry": {"type": "polygon", "coordinates": [[42.296441539941, -82.66197309544255], [42.2960583754255, -82.66195611125993], [42.29607617273872, -82.66271049164818], [42.29644886820302, -82.66270199955076]]}, "properties": {}}, {"id": "area_2", "name": "warehouse", "type": "building", "geometry": {"type": "polygon", "coordinates": [[42.2956037604317, -82.66209304332735], [42.295533628866124, -82.66209429118956], [42.29553216869182, -82.6623469692514], [42.29561539836005, -82.66234104712748]]}, "properties": {"height": 8}}, {"id": "area_3", "name": "forest", "type": "woodland", "geometry": {"type": "polygon", "coordinates": [[42.29602833822704, -82.6611113548279], [42.29603839499092, -82.65998294950887], [42.29463146264805, -82.65998912414624], [42.294610799330975, -82.66115630230765]]}, "properties": {}}, {"id": "area_4", "name": "farmland_1", "type": "agricultural", "geometry": {"type": "polygon", "coordinates": [[42.296105987033755, -82.66194864827698], [42.296086178491265, -82.66111846788824], [42.29414887143721, -82.66119880793259], [42.29417660425833, -82.66358222901644], [42.29531363987361, -82.66353938099742], [42.29530967810206, -82.66200756430021]]}, "properties": {}}, {"id": "area_5", "name": "farmland_2", "type": "agricultural", "geometry": {"type": "polygon", "coordinates": [[42.295499844004155, -82.66530150559512], [42.295425206683475, -82.66373991966249], [42.29417660590377, -82.66387680893385], [42.29426772798603, -82.66544076166272]]}, "properties": {}}, {"id": "area_6", "name": "road_1", "type": "road", "geometry": {"type": "linestring", "coordinates": [[42.29536911002327, -82.66359829684507], [42.29693002305041, -82.66351260078383]]}, "properties": {"width": 10}}, {"id": "area_7", "name": "road_2", "type": "road", "geometry": {"type": "linestring", "coordinates": [[42.29691417640369, -82.66347510875849], [42.297052834367285, -82.6612416557528]]}, "properties": {"width": 10}}, {"id": "area_8", "name": "yard", "type": "land", "geometry": {"type": "polygon", "coordinates": [[42.296244652305, -82.66274669243523], [42.296240690592015, -82.66194864805497], [42.29532949243418, -82.66198614008032], [42.29537361790283, -82.6628226041794]]}, "properties": {}}]}, "user_requirements": [{"task_id": "1", "description": "Assist in rapidly monitoring the nearby forest areas.", "description_cn": "协助监测森林区域"}, {"task_id": "2", "description": "Conduct quick farmland monitoring and detailed warehouse scanning.", "description_cn": "进行农田监测和仓库的建筑扫描"}, {"task_id": "3", "description": "Conduct road inspection.", "description_cn": "执行道路检查"}, {"task_id": "4", "description": "Execute agricultural zone scanning with 70% frontal overlap, 40% lateral overlap, and a ground sampling distance (GSD) of 0.9 cm/pixel.", "description_cn": "执行农业区域扫描，前向重叠度为70%，侧向重叠度为40%，地面采样距离为0.9厘米/像素"}, {"task_id": "5", "description": "Execute sequential navigation at a constant altitude of 40 meters through the following waypoints in designated order:[[42.29466064621194,-82.65989487315737],[42.29465191789008,-82.66121646253347],[42.2969256036579,-82.66115746300308],[42.29692123964465,-82.65986537341772]]", "description_cn": "按照指定顺序依次经过以下航点"}, {"task_id": "7", "description": "Check the roof of the house for any issues.", "description_cn": "检查一下房子的屋顶有没有什么问题。"}, {"task_id": "8", "description": "Fly along the edge of the forest and make sure everything looks normal.", "description_cn": "沿着森林边缘飞一圈，看看一切是否正常。"}, {"task_id": "9", "description": "Capture high-resolution imagery of the farmland to check crop health.", "description_cn": "拍摄农田的高分辨率图像，用于检查作物健康状况。"}, {"task_id": "10", "description": "Inspect the exterior walls of the warehouse, especially the upper parts.", "description_cn": "检查仓库的外墙，特别是上半部分。"}, {"task_id": "11", "description": "Get a visual survey of the yard area.", "description_cn": "对院子区域进行一次勘察。"}, {"task_id": "12", "description": "Fly a path along the road (road_1) and take some photos.", "description_cn": "沿着主路 (road_1) 飞行一段路线并拍几张照片。"}, {"task_id": "13", "description": "Perform a quick scan over the entire property including the house and yard.", "description_cn": "对包括房屋和院子在内的整个区域进行一次快速扫描。"}, {"task_id": "14", "description": "Perform a detailed inspection of the bridge south of the farmland.", "description_cn": "对农田南边的桥梁进行详细检查。"}, {"task_id": "15", "description": "Perform a polygon scan of road_2 with 80% overlap.", "description_cn": "对 road_2 进行80%重叠度的多边形扫描。"}]}