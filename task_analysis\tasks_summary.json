[{"task_id": "1", "description": "Assist in rapidly monitoring the nearby forest areas.", "task_types": ["Mapping Mission"], "subtask_count": 1}, {"task_id": "2", "description": "Conduct quick farmland monitoring and detailed warehouse scanning.", "task_types": ["Mapping Mission", "Structure Scan Mission"], "subtask_count": 3}, {"task_id": "3", "description": "Conduct road inspection.", "task_types": ["Corridor Scan Mission"], "subtask_count": 2}, {"task_id": "4", "description": "Execute agricultural zone scanning with 70% frontal overlap, 40% lateral overlap, and a ground sampling distance (GSD) of 0.9 cm/pixel.", "task_types": ["Mapping Mission"], "subtask_count": 2}, {"task_id": "5", "description": "Execute sequential navigation at a constant altitude of 40 meters through the following waypoints in designated order:[[42.29466064621194,-82.65989487315737],[42.29465191789008,-82.66121646253347],[42.2969256036579,-82.66115746300308],[42.29692123964465,-82.65986537341772]]", "task_types": ["Waypoint Mission"], "subtask_count": 1}, {"task_id": "7", "description": "Check the roof of the house for any issues.", "task_types": ["Structure Scan Mission"], "subtask_count": 1}, {"task_id": "8", "description": "Fly along the edge of the forest and make sure everything looks normal.", "task_types": ["Corridor Scan Mission"], "subtask_count": 1}, {"task_id": "9", "description": "Capture high-resolution imagery of the farmland to check crop health.", "task_types": ["Mapping Mission"], "subtask_count": 2}, {"task_id": "10", "description": "Inspect the exterior walls of the warehouse, especially the upper parts.", "task_types": ["Structure Scan Mission"], "subtask_count": 1}, {"task_id": "11", "description": "Get a visual survey of the yard area.", "task_types": ["Mapping Mission"], "subtask_count": 1}, {"task_id": "12", "description": "Fly a path along the road (road_1) and take some photos.", "task_types": ["Corridor Scan Mission"], "subtask_count": 1}, {"task_id": "13", "description": "Perform a quick scan over the entire property including the house and yard.", "task_types": ["Mapping Mission", "Structure Scan Mission"], "subtask_count": 4}, {"task_id": "14", "description": "Perform a detailed inspection of the bridge south of the farmland.", "task_types": ["Structure Scan Mission"], "subtask_count": 1}, {"task_id": "15", "description": "Perform a polygon scan of road_2 with 80% overlap.", "task_types": ["Corridor Scan Mission"], "subtask_count": 1}]