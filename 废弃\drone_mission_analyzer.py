import json
import os
import sys
from openai import OpenAI
from typing import Dict, List, Any, Optional
import argparse

class DroneMissionAnalyzer:
    """
    无人机航拍任务分析器，使用大语言模型来分析和分类任务
    """

    def __init__(self, api_key: str, model: str = "gpt-4o", base_url: str = "https://api.openai.com/v1"):
        """
        初始化分析器

        Args:
            api_key: LLM API密钥
            model: 使用的模型名称
            base_url: API基础URL
        """
        self.api_key = api_key
        self.model = model
        self.base_url = base_url
        self.client = OpenAI(
            base_url=base_url,
            api_key=api_key
        )

    def load_mission_data(self, file_path: str) -> Dict:
        """
        加载任务数据JSON文件

        Args:
            file_path: JSON文件路径

        Returns:
            解析后的任务数据字典
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                return json.load(file)
        except Exception as e:
            print(f"Error loading mission data: {e}")
            sys.exit(1)

    def generate_prompt(self, task_id: str, description: str, target_areas: List[Dict],
                        home_position: List, flight_restricted_area: Dict) -> str:
        """
        Generate a prompt for the LLM

        Args:
            task_id: Task ID
            description: Task description
            target_areas: List of target areas
            home_position: Starting position
            flight_restricted_area: Flight restricted area

        Returns:
            Formatted prompt string
        """
        # Create simplified target area information for easy reference
        target_areas_simplified = []
        for area in target_areas:
            target_areas_simplified.append({
                "id": area["id"],
                "name": area["name"],
                "type": area["type"]
            })

        target_areas_simple_text = json.dumps(target_areas_simplified, indent=2)
        target_areas_text = json.dumps(target_areas, indent=2)


        prompt = f"""
        You are an AI assistant specialized in drone aerial photography mission planning. Analyze the following drone mission requirements and classify them into appropriate task types.

        Task Types:
        1. (Simple waypoint task) - Navigate between specific waypoints
        2. (Survey task) - Systematically scan an area for mapping or monitoring
        3. (Corridor scan task) - Scan along linear features (roads, pipelines, rivers) 
        4. (Structure scan task) - Detailed scanning around buildings (buildings, monuments)

        Current Task:
        Task ID: {task_id}
        Task Description: "{description}"

        Available Target Areas (Simplified):
        {target_areas_simple_text}

        Detailed Target Areas:
        {target_areas_text}

        Professional Standards for Drone Aerial Photography Parameters:

        1. Frontal Overlap Rate (frontal_overlap):
        - High-precision: 80%-90% (archaeological sites, detailed structural analysis)
        - Standard: 70%-80% (general mapping, terrain modeling)
        - Rapid: 55%-70% (quick assessments, preliminary surveys)

        2. Lateral Overlap Rate (lateral_overlap):
        - High-precision: 65%-80% (detailed orthomosaic creation, accurate volumetric calculations)
        - Standard: 55%-65% (topographic mapping, general site documentation)
        - Rapid: 35%-55% (reconnaissance, large area coverage)

        3. Ground Sampling Distance (GSD) and Corresponding Flight Height:
        - High-precision: 0.5-1.0 cm/pixel
        - Medium-precision: 1.0-2.5 cm/pixel
        - Low-precision: 2.5-8.0 cm/pixel

        Analysis Process:
        1. Analyze the task description to identify requirements.
        2. Identify target areas by matching keywords or implicit references.
        3. Verify if the requested target areas exist in the available target areas list.
        4. For each identified target or requirement:
           a. If the target area exists, proceed with task classification.
           b. If the target area does not exist, mark the subtask as infeasible and provide a clear explanation.
           c. If the task contains multiple requirements, analyze each separately - some may be feasible while others are not.
        5. Classify the feasible tasks into one or more of the four types.
        6. For multiple target areas or task types, create separate subtasks.
        7. Extract specific parameters using these field names:
        - Building height: "height" (in meters)
        - Corridor width: "width" (in meters)
        - Frontal overlap rate: "frontal_overlap" (integer, e.g., 70 for 70%)
        - Lateral overlap rate: "lateral_overlap" (integer, e.g., 60 for 60%)
        - Ground sampling distance: "gsd" (e.g., 2.5 for 2.5 cm/pixel)
        8. Required parameters by task type:
        - Survey, Corridor scan, Structure scan: must include "frontal_overlap", "lateral_overlap", "gsd"
        - Corridor scan: must additionally include "width"
        - Structure scan: must additionally include "height"
        - Simple waypoint task: must include "height" (default: 30)
        9. Parameter Selection Guidelines:
        - Select specific values within appropriate ranges based on context clues in the task description
        - Consider the precise wording of requirements (e.g., "very detailed" vs "somewhat detailed" should yield different GSD values)
        - Account for target area characteristics when choosing parameter values
        - When selecting parameters, consider contextual factors like:
            * Size of area (larger areas might need higher GSD/lower resolution)
            * Complexity of features (more complex = higher overlap needed)
            * Time constraints (tighter deadlines = slightly less overlap acceptable)
            * Purpose of imagery (analysis type impacts precision needs)
        10. Error handling:
        - For Corridor scan without width: add error "insufficient parameters for corridor scan task, unable to continue execution"
        - For Structure scan without height: add error "insufficient parameters for structure scan task, unable to continue execution"
        - For Simple waypoint task without height: add error "insufficient parameters for waypoints task, unable to continue execution"

        Response Format:
        Provide a valid JSON object with this structure:
        {{
            "task_id": "{task_id}",
            "description": "{description}",
            "home_position": {json.dumps(home_position)},
            "flight_restricted_area": {json.dumps(flight_restricted_area)},
            "task_types": ["List of all task types involved"],
            "task_summary": "A comprehensive summary of the entire task analysis in 3-5 sentences, including what can be done, what cannot be done, and why. Use clear, concise language suitable for end-users.",
            "feasibility_analysis": "Overall analysis of task feasibility based on available target areas and requirements",
            "subtasks": [
                {{
                    "subtask_id": "1",
                    "type": "Task type (one of the four types)",
                    "target_area_id": "Target area ID (if applicable)",
                    "target_area_name": "Target area name (if applicable)",
                    "feasible": true/false,
                    "feasibility_reason": "Explanation of why this subtask is feasible or not",
                    "geometry": {{
                        "type": "polygon or linestring or multipoint (for Simple waypoint tasks)",
                        "coordinates": [...]
                    }},
                    "parameters": {{
                        // Parameter requirements by task type:
                        // Survey, Corridor scan, Structure scan: "frontal_overlap", "lateral_overlap", "gsd"
                        // Corridor scan: additionally "width"
                        // Structure scan: additionally "height"
                        // Simple waypoint task: "height" (default: 30)
                    }},
                    "error": "Error message if required parameters are missing"
                }}
            ]
        }}

        Important Rules:
        - First check if the requested target areas or features exist in the available target areas list.
        - If a requested target area does not exist (e.g., "library" when no library is in the target areas), mark the subtask as infeasible with feasible: false and explain why in feasibility_reason.
        - For tasks with multiple requirements, analyze each separately - some may be feasible while others are not.
        - Use geometry information if task doesn't reference specific target areas but includes explicit waypoints.
        - Create separate subtasks for each target area if task matches multiple areas.
        - Create separate subtasks for combined tasks (e.g., "farmland survey and warehouse scanning").
        - Use exact parameter field names: height, width, frontal_overlap, lateral_overlap, gsd.
        - For waypoint tasks, add waypoints to geometry field with type "multipoint".
        - Ensure valid JSON structure.
        - When encountering terms like "high quality," "standard," or "rapid," select a specific value from the corresponding range that best fits the precise context.
        - If a task is infeasible, still include it in the subtasks list with feasible: false, but you may omit parameters and geometry.
        - Provide a clear overall feasibility_analysis that summarizes which parts of the task can be executed and which cannot.
        - The task_summary field should provide a comprehensive yet concise summary (3-5 sentences) of the entire task analysis in user-friendly language. It should explain:
          * What the user requested
          * What parts of the request can be fulfilled and how
          * What parts cannot be fulfilled and why
          * Any alternative suggestions if applicable
          * Use clear, non-technical language that end-users can easily understand

        Output only valid JSON, without explanation or additional text.
        """
        return prompt


    def call_llm_api(self, prompt: str) -> str:
        """
        使用OpenAI客户端库调用大语言模型API

        Args:
            prompt: 提示字符串

        Returns:
            API响应内容
        """
        try:
            completion = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are a drone mission planning assistant, specializing in analyzing mission requirements and categorizing them into specific mission types, as well as designing appropriate aerial photography parameters for the mission."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3
            )

            return completion.choices[0].message.content

        except Exception as e:
            print(f"Error calling LLM API: {e}")
            raise

    def parse_llm_response(self, response: str, task_id: str, description: str) -> Dict:
        """
        解析LLM响应为所需的JSON格式

        Args:
            response: LLM响应文本
            task_id: 任务ID（用于回退）
            description: 任务描述（用于回退）

        Returns:
            解析后的任务JSON
        """
        try:
            # 提取JSON部分
            json_start = response.find('{')
            json_end = response.rfind('}') + 1

            if json_start == -1 or json_end == 0:
                raise ValueError("响应中未找到JSON")

            json_str = response[json_start:json_end]

            # 解析JSON
            task_json = json.loads(json_str)

            # 确保JSON具有所需的结构
            if "task_id" not in task_json:
                task_json["task_id"] = task_id

            if "description" not in task_json:
                task_json["description"] = description

            if "subtasks" not in task_json:
                task_json["subtasks"] = []

            if "task_types" not in task_json:
                # 尝试从子任务派生任务类型
                task_types = set()
                for subtask in task_json.get("subtasks", []):
                    if "type" in subtask:
                        task_types.add(subtask["type"])
                task_json["task_types"] = list(task_types)

            return task_json

        except Exception as e:
            print(f"Error parsing LLM response: {e}")
            print(f"Response: {response}")

            # 创建最小有效JSON作为回退
            return {
                "task_id": task_id,
                "description": description,
                "task_types": ["unknown"],
                "task_summary": "无法完成任务分析。系统在处理您的请求时遇到了问题，无法生成有效的任务计划。请检查您的任务描述是否清晰，并确保所请求的目标区域在可用区域列表中。",
                "feasibility_analysis": "无法解析任务，请检查任务描述和可用区域",
                "subtasks": [],
                "error": str(e),
                "original_response": response
            }

    def save_results(self, results: List[Dict], output_dir: str) -> None:
        """
        将结果保存到JSON文件

        Args:
            results: 任务分析结果列表
            output_dir: 输出目录
        """
        os.makedirs(output_dir, exist_ok=True)

        # 保存每个任务到单独的文件
        for result in results:
            task_id = result["task_id"]
            task_file = f"{output_dir}/task_{task_id}.json"

            with open(task_file, 'w', encoding='utf-8') as file:
                json.dump(result, file, indent=2, ensure_ascii=False)

            print(f"Saved task {task_id} to {task_file}")

        # 保存所有任务到单个文件
        all_tasks_file = f"{output_dir}/all_tasks.json"
        with open(all_tasks_file, 'w', encoding='utf-8') as file:
            json.dump(results, file, indent=2, ensure_ascii=False)

        print(f"Saved all tasks to {all_tasks_file}")

        # 创建摘要文件
        summary = []
        for result in results:
            summary_item = {
                "task_id": result["task_id"],
                "description": result["description"],
                "task_types": result["task_types"],
                "subtask_count": len(result["subtasks"])
            }

            # 添加任务总结到摘要（如果存在）
            if "task_summary" in result:
                summary_item["task_summary"] = result["task_summary"]

            # 添加可行性分析到摘要（如果存在）
            if "feasibility_analysis" in result:
                summary_item["feasibility_analysis"] = result["feasibility_analysis"]

            summary.append(summary_item)

        summary_file = f"{output_dir}/tasks_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as file:
            json.dump(summary, file, indent=2, ensure_ascii=False)

        print(f"Saved summary to {summary_file}")

    def process_task(self, task: Dict, mission_prior: Dict) -> Dict:
        """
        处理单个任务

        Args:
            task: 任务数据
            mission_prior: 任务先验信息

        Returns:
            处理后的任务JSON
        """
        task_id = task["task_id"]
        description = task["description"]

        print(f"Processing task {task_id}: {description}")

        # 生成LLM提示
        prompt = self.generate_prompt(
            task_id,
            description,
            mission_prior["target_areas"],
            mission_prior["home_position"],
            mission_prior["flight_restricted_area"]
        )

        # 调用LLM API
        response = self.call_llm_api(prompt)

        # 解析响应
        task_json = self.parse_llm_response(response, task_id, description)

        print(f"Completed task {task_id}")
        return task_json

    def analyze_mission(self, input_file: str, output_dir: str) -> None:
        """
        分析整个任务数据

        Args:
            input_file: 输入JSON文件路径
            output_dir: 输出目录
        """
        print(f"Starting drone mission analysis")
        print(f"Input file: {input_file}")
        print(f"Output directory: {output_dir}")
        print(f"Using model: {self.model}")
        print(f"Using API base URL: {self.base_url}")

        # 加载任务数据
        mission_data = self.load_mission_data(input_file)
        print(f"Loaded mission data with {len(mission_data.get('user_requirements', []))} tasks")

        mission_prior = mission_data["mission_prior"]
        user_requirements = mission_data["user_requirements"]

        # 处理所有任务
        results = []
        for task in user_requirements:
            try:
                task_json = self.process_task(task, mission_prior)
                results.append(task_json)
            except Exception as e:
                print(f"Error processing task {task.get('task_id', 'unknown')}: {e}")

                # 添加错误条目到结果
                results.append({
                    "task_id": task.get("task_id", "unknown"),
                    "description": task.get("description", ""),
                    "error": str(e),
                    "task_types": ["error"],
                    "task_summary": "任务处理失败。系统在分析您的请求时遇到了技术问题，无法完成任务规划。请稍后重试或修改您的任务描述。",
                    "feasibility_analysis": "处理任务时发生错误，无法确定任务可行性",
                    "subtasks": []
                })

        # 保存结果
        self.save_results(results, output_dir)

        print(f"Analysis complete. Results saved to {output_dir}")

def analyze_mission():
    """主函数"""
    parser = argparse.ArgumentParser(description='分析无人机任务需求并将其分类为任务类型')
    parser.add_argument('--input_file', default="experiment/env_3/exp_env_3.json",help='包含任务数据的输入JSON文件路径')
    parser.add_argument('--output_dir', default="experiment/env_3/task_analysis",help='保存输出JSON文件的目录')
    parser.add_argument('--api-key', default="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",help='LLM服务的API密钥')
    parser.add_argument('--model', default="openai/gpt-4.1", help='要使用的LLM模型 (默认: gpt-4o)')
    parser.add_argument('--api-url', default="https://openrouter.ai/api/v1/", help='API端点URL')

    # anthropic/claude-3.7-sonnet        google/gemini-2.5-pro-preview    google/gemini-2.5-flash-preview
    # parser.add_argument('--api-key', default="sk-df0cb63f37e240699f78a373ce764daa",help='LLM服务的API密钥')
    # parser.add_argument('--model', default="deepseek-chat", help='要使用的LLM模型 (默认: gpt-4o)')
    # parser.add_argument('--api-url', default="https://api.deepseek.com", help='API端点URL')

    args = parser.parse_args()

    # 创建分析器并运行
    analyzer = DroneMissionAnalyzer(args.api_key, args.model, args.api_url)
    analyzer.analyze_mission(args.input_file, args.output_dir)

if __name__ == "__main__":
    analyze_mission()
