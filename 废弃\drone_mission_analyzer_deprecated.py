import json
import os
import sys
from openai import OpenAI
from typing import Dict, List, Any, Optional
import argparse

class DroneMissionAnalyzer:
    """
    无人机航拍任务分析器，使用大语言模型来分析和分类任务
    """

    def __init__(self, api_key: str, model: str = "gpt-4o", base_url: str = "https://api.openai.com/v1"):
        """
        初始化分析器

        Args:
            api_key: LLM API密钥
            model: 使用的模型名称
            base_url: API基础URL
        """
        self.api_key = api_key
        self.model = model
        self.base_url = base_url
        self.client = OpenAI(
            base_url=base_url,
            api_key=api_key
        )

    def load_mission_data(self, file_path: str) -> Dict:
        """
        加载任务数据JSON文件

        Args:
            file_path: JSON文件路径

        Returns:
            解析后的任务数据字典
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                return json.load(file)
        except Exception as e:
            print(f"Error loading mission data: {e}")
            sys.exit(1)

    def generate_prompt(self, task_id: str, description: str, target_areas: List[Dict],
                        home_position: List, flight_restricted_area: Dict) -> str:
        """
        生成LLM提示词

        参数:
            task_id: 任务ID
            description: 任务描述
            target_areas: 目标区域列表
            home_position: 起始位置
            flight_restricted_area: 飞行限制区域

        返回:
            格式化后的提示字符串
        """
        # 创建简化的目标区域信息便于参考
        target_areas_simplified = []
        for area in target_areas:
            target_areas_simplified.append({
                "id": area["id"],
                "name": area["name"],
                "type": area["type"]
            })

        target_areas_simple_text = json.dumps(target_areas_simplified, indent=2)
        target_areas_text = json.dumps(target_areas, indent=2)

        prompt = f"""
        你是一个专门从事无人机航拍任务规划的AI助手。你的任务是分析以下无人机任务需求，并将其分类到适当的任务类型中。

        任务类型：
        1. (Simple waypoint task) - 在特定航点间导航
        2. (Survey task) - 系统扫描区域，通常用于测绘或监测
        3. (Corridor scan task) - 沿线性特征扫描（如道路，河流，海岸线）
        4. (Structure scan task) - 对三维结构进行详细扫描（如建筑物）

        当前任务：
        任务ID: {task_id}
        任务描述: "{description}"

        可用目标区域（简化版）:
        {target_areas_simple_text}

        详细目标区域:
        {target_areas_text}

        起始位置:
        {json.dumps(home_position)}

        飞行限制区域:
        {json.dumps(flight_restricted_area, indent=2)}

        无人机航拍参数专业标准：
        1. 航向重叠率（frontal_overlap）：
        - 高精度建模测绘：75%-85%
        - 标准测绘任务：70%-75%
        - 快速测绘任务：60%-70%

        2. 旁向重叠率（lateral_overlap）：
        - 高精度建模测绘：60%-70%
        - 标准测绘任务：50%-60%
        - 快速测绘任务：40%-50%

        3. 地面采样距离（GSD）：
        - 高精度需求：0.5-1.0 厘米/像素
        - 中精度需求：1.0-5.0 厘米/像素
        - 低精度快速扫描：5.0-16.0 厘米/像素

        4. 任务类型默认参数：
        - (Simple waypoint task): 简单的航点任务，不需要设置 frontal_overlap, lateral_overlap, gsd 参数，高度的设置需要根据用户的描述来确定
        - (Survey task): frontal_overlap=70%, lateral_overlap=50%
        - (Corridor scan task): frontal_overlap=70%, lateral_overlap=45%
        - (Structure scan task): frontal_overlap=75%, lateral_overlap=60%

        分析指南：
        1. 仔细分析任务描述
        2. 通过关键词匹配或隐式引用确定涉及的目标区域
        3. 将任务分类到1-4种类型中的一种或多种任务的组合
        4. 如果任务涉及多个目标区域或多个任务类型，需创建独立子任务
        5. 提取任务描述中提到的特定参数，使用指定参数字段名称：
        - 建筑高度: "height"
        - 走廊宽度: "width"
        - 航向重叠率: "frontal_overlap"
        - 旁向重叠率: "lateral_overlap"
        - 地面采样距离: "gsd"
        6. 除(Simple waypoint task)外，所有任务类型必须添加以下参数：
        - frontal_overlap
        - lateral_overlap
        - gsd
        7. 根据任务描述智能设置GSD值：
        - 根据目标区域的经纬度之间的距离，来简单估计GSD值，如果经纬度跨度较大，无人机需要飞行较远的距离，则可以设置较大的GSD（10.0-16.0厘米/像素）
        - 如果描述包含"高精度"、"高质量"、"详细"、"精确"等关键词，使用较低GSD值（0.5-1.0厘米/像素）
        - 如果描述包含"快速"、"高效"、"大范围"、"快速"等关键词，使用较高GSD值（5.0-16.0厘米/像素）
        - 其他情况使用中等GSD值（1.0-5.0厘米/像素）
        - 如果明确指定GSD值，直接使用该值
        8. (Corridor scan task)必须包含"width"参数，否则在输出中添加错误信息"走廊扫描任务参数不足，无法继续执行"
        9. (Survey task)必须包含"height"参数，否则在输出中添加错误信息"建筑扫描任务参数不足，无法继续执行"
        10. 航点任务如果提供明确航点，需将这些航点添加到geometry字段，类型必须设为"multipoint"

        响应格式：
        提供符合以下结构的有效JSON对象：
        {{
            "task_id": "{task_id}",
            "description": "{description}",
            "home_position": {json.dumps(home_position)},
            "flight_restricted_area": {json.dumps(flight_restricted_area)},
            "task_types": ["本任务涉及的所有任务类型列表"],
            "subtasks": [
                {{
                    "subtask_id": "1",
                    "type": "任务类型（四种类型之一）",
                    "target_area_id": "目标区域ID（如适用）",
                    "target_area_name": "目标区域名称（如适用）",
                    "geometry": {{
                        "type": "polygon/linestring/multipoint",
                        "coordinates": [...]
                    }},
                    "parameters": {{
                        // 根据任务类型使用以下参数名称：
                        // 勘测、走廊扫描、建筑扫描任务必须包含："frontal_overlap", "lateral_overlap", "gsd"
                        // 简单航点任务：不需要航拍参数
                        // 走廊扫描任务必须额外包含："width"
                        // 建筑扫描任务必须额外包含："height"
                        // 航点任务必须额外包含："height"，默认值30
                        // GSD值必须在0.5-15.0厘米/像素范围内
                    }},
                    "error": "如果是(Corridor scan task)但缺少width参数，添加'走廊扫描任务参数不足，无法继续执行'",
                    "error": "如果是(Structure scan task)但缺少height参数，添加'建筑扫描任务参数不足，无法继续执行'",
                    "error": "如果是(Simple waypoint task)但缺少height参数，添加'航点任务参数不足，无法继续执行'"
                }}
                // 更多子任务（如适用）
            ]
        }}

        重要注意事项：
        - 如果任务未引用特定目标区域但包含明确几何信息（如航点），应使用该几何信息
        - 航点任务必须将航点列表添加到geometry字段，类型设为"multipoint"
        - 走廊扫描任务必须包含width参数，否则添加错误信息
        - 建筑扫描任务必须包含height参数，否则添加错误信息
        - 每个任务（除简单航点任务外）必须包含frontal_overlap、lateral_overlap和gsd参数，根据任务类型使用适当默认值
        - GSD值必须在0.5-15.0厘米/像素范围内，并根据任务描述中的精度/效率要求调整
        - 如果任务引用某类区域匹配多个目标区域，应为每个区域创建子任务
        - 对于组合任务，需创建独立子任务
        - 参数字段名称必须严格遵循指定命名：height, width, frontal_overlap, lateral_overlap, gsd
        - 确保JSON有效并严格遵循指定结构

        只输出有效JSON，不要包含解释或额外文本。
        """
        return prompt


    def call_llm_api(self, prompt: str) -> str:
        """
        使用OpenAI客户端库调用大语言模型API

        Args:
            prompt: 提示字符串

        Returns:
            API响应内容
        """
        try:
            completion = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是一个无人机任务规划助手，专门分析任务需求并将其分类为特定任务类型。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1  # 较低的温度以获得更确定性的输出
            )

            return completion.choices[0].message.content

        except Exception as e:
            print(f"Error calling LLM API: {e}")
            raise

    def parse_llm_response(self, response: str, task_id: str, description: str) -> Dict:
        """
        解析LLM响应为所需的JSON格式

        Args:
            response: LLM响应文本
            task_id: 任务ID（用于回退）
            description: 任务描述（用于回退）

        Returns:
            解析后的任务JSON
        """
        try:
            # 提取JSON部分
            json_start = response.find('{')
            json_end = response.rfind('}') + 1

            if json_start == -1 or json_end == 0:
                raise ValueError("响应中未找到JSON")

            json_str = response[json_start:json_end]

            # 解析JSON
            task_json = json.loads(json_str)

            # 确保JSON具有所需的结构
            if "task_id" not in task_json:
                task_json["task_id"] = task_id

            if "description" not in task_json:
                task_json["description"] = description

            if "subtasks" not in task_json:
                task_json["subtasks"] = []

            if "task_types" not in task_json:
                # 尝试从子任务派生任务类型
                task_types = set()
                for subtask in task_json.get("subtasks", []):
                    if "type" in subtask:
                        task_types.add(subtask["type"])
                task_json["task_types"] = list(task_types)

            return task_json

        except Exception as e:
            print(f"Error parsing LLM response: {e}")
            print(f"Response: {response}")

            # 创建最小有效JSON作为回退
            return {
                "task_id": task_id,
                "description": description,
                "task_types": ["unknown"],
                "subtasks": [],
                "error": str(e),
                "original_response": response
            }

    def save_results(self, results: List[Dict], output_dir: str) -> None:
        """
        将结果保存到JSON文件

        Args:
            results: 任务分析结果列表
            output_dir: 输出目录
        """
        os.makedirs(output_dir, exist_ok=True)

        # 保存每个任务到单独的文件
        for result in results:
            task_id = result["task_id"]
            task_file = f"{output_dir}/task_{task_id}.json"

            with open(task_file, 'w', encoding='utf-8') as file:
                json.dump(result, file, indent=2, ensure_ascii=False)

            print(f"Saved task {task_id} to {task_file}")

        # 保存所有任务到单个文件
        all_tasks_file = f"{output_dir}/all_tasks.json"
        with open(all_tasks_file, 'w', encoding='utf-8') as file:
            json.dump(results, file, indent=2, ensure_ascii=False)

        print(f"Saved all tasks to {all_tasks_file}")

        # 创建摘要文件
        summary = []
        for result in results:
            summary.append({
                "task_id": result["task_id"],
                "description": result["description"],
                "task_types": result["task_types"],
                "subtask_count": len(result["subtasks"])
            })

        summary_file = f"{output_dir}/tasks_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as file:
            json.dump(summary, file, indent=2, ensure_ascii=False)

        print(f"Saved summary to {summary_file}")

    def process_task(self, task: Dict, mission_prior: Dict) -> Dict:
        """
        处理单个任务

        Args:
            task: 任务数据
            mission_prior: 任务先验信息

        Returns:
            处理后的任务JSON
        """
        task_id = task["task_id"]
        description = task["description"]

        print(f"Processing task {task_id}: {description}")

        # 生成LLM提示
        prompt = self.generate_prompt(
            task_id,
            description,
            mission_prior["target_areas"],
            mission_prior["home_position"],
            mission_prior["flight_restricted_area"]
        )

        # 调用LLM API
        response = self.call_llm_api(prompt)

        # 解析响应
        task_json = self.parse_llm_response(response, task_id, description)

        print(f"Completed task {task_id}")
        return task_json

    def analyze_mission(self, input_file: str, output_dir: str) -> None:
        """
        分析整个任务数据

        Args:
            input_file: 输入JSON文件路径
            output_dir: 输出目录
        """
        print(f"Starting drone mission analysis")
        print(f"Input file: {input_file}")
        print(f"Output directory: {output_dir}")
        print(f"Using model: {self.model}")
        print(f"Using API base URL: {self.base_url}")

        # 加载任务数据
        mission_data = self.load_mission_data(input_file)
        print(f"Loaded mission data with {len(mission_data.get('user_requirements', []))} tasks")

        mission_prior = mission_data["mission_prior"]
        user_requirements = mission_data["user_requirements"]

        # 处理所有任务
        results = []
        for task in user_requirements:
            try:
                task_json = self.process_task(task, mission_prior)
                results.append(task_json)
            except Exception as e:
                print(f"Error processing task {task.get('task_id', 'unknown')}: {e}")

                # 添加错误条目到结果
                results.append({
                    "task_id": task.get("task_id", "unknown"),
                    "description": task.get("description", ""),
                    "error": str(e),
                    "task_types": ["error"],
                    "subtasks": []
                })

        # 保存结果
        self.save_results(results, output_dir)

        print(f"Analysis complete. Results saved to {output_dir}")

def analyze_mission():
    """主函数"""
    parser = argparse.ArgumentParser(description='分析无人机任务需求并将其分类为任务类型')
    parser.add_argument('--input_file', default="task_env_guzhenkou.json",help='包含任务数据的输入JSON文件路径')
    parser.add_argument('--output_dir', default="task_analysis",help='保存输出JSON文件的目录')
    parser.add_argument('--api-key', default="sk-df0cb63f37e240699f78a373ce764daa",help='LLM服务的API密钥')
    parser.add_argument('--model', default="deepseek-chat", help='要使用的LLM模型 (默认: gpt-4o)')
    parser.add_argument('--api-url', default="https://api.deepseek.com/v1", help='API端点URL')

    args = parser.parse_args()

    # 创建分析器并运行
    analyzer = DroneMissionAnalyzer(args.api_key, args.model, args.api_url)
    analyzer.analyze_mission(args.input_file, args.output_dir)

if __name__ == "__main__":
    analyze_mission()
